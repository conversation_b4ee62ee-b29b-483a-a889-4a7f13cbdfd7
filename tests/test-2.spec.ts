import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('http://localhost:5173/login');
  await page.getByRole('button', { name: 'google icon Continue with' }).click();
  await page.getByRole('textbox', { name: 'Email or phone' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Enter your password' }).press('Enter');
  await page.getByRole('textbox', { name: 'Enter your password' }).fill('PiCode@3.141');
  await page.getByRole('textbox', { name: 'Enter your password' }).press('Enter');
  await page.goto('http://localhost:5173/');
  await expect(page.getByText('What will you build today?')).toBeVisible();
});