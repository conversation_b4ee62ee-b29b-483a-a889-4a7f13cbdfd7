import { useEffect, useState, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { motion, AnimatePresence } from "motion/react";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { cleanupTabStatesInLocalStorage } from "@/utils/tabStateCleanup";

import { useGetJobsQuery, useGetConfigQuery } from "@/store/api/apiSlice";
import { Spinner } from "../../components/ui/spinner";

import { useTabState } from "../../components/TabBar";

import { useAuth, useCredits } from "@/contexts";
import EmbeddedTask from "../createTask/EmbeddedTask";
import ShowCase from "@/components/ShowCase";
import WelcomeScreen from "@/components/DownloadSection";

// Assets
import "@/styles/welcome-modal.css";
import RecentTasks from "@/components/RecentTask";
import JobIDModal from "@/components/modals/JobIDModal";
import { Job } from "@/types/job";
import { generateUniqueTabId } from "@/lib/utils/uiUtils";
import { useGitHub } from "@/hooks/useGitHubAPI";
import FadingDotLine from "@/components/FadingDotLine";
import Banner from "@/assets/banner.svg";
// @ts-ignore
import BgVideo from "@/assets/bg-vid.mp4";
import PulseDot from "@/components/PulseDot";
import {
  hasSeenAssetModal,
  markAssetModalAsSeen,
} from "@/lib/utils/modalStateManager";
import AssetModal from "@/components/modals/introduction/AssetModal";
import { useIsUploadAssetEnabled } from "@/services/postHogService";
import { ReferralModal } from "@/components/modals/referral/ReferralModal";
import ProBanner from "@/components/modals/pro/ProBanner";
import { ProModal } from "@/components/modals/pro/ProModal";
import { useProModeModal } from "@/hooks/useProModeModal";
import { ac } from "react-router/dist/development/route-data-aSUFWnQ6";
import { fetchJobs } from "@/store/jobsSlice";

export default function Home() {
  const { data: jobs = [], isLoading: jobLoading, refetch: refetchJobs } = useGetJobsQuery();
  const { data: globalConfig, isLoading: configLoading } = useGetConfigQuery();
  const [loading, setLoading] = useState(false);
  const [reloading, setReloading] = useState(false);
  const [isJobIdDialogOpen, setIsJobIdDialogOpen] = useState(false);
  const [jobIdInput, setJobIdInput] = useState("");
  const [isBannerVisible, setIsBannerVisible] = useState(
    globalConfig?.overload_lock?.enabled || false
  );
  const [isFeatureModalOpen, setIsFeatureModalOpen] = useState(false);
  const [isReferralModalOpen, setIsReferralModalOpen] = useState(false);
  const [isCustomAgentsModalOpen, setIsCustomAgentsModalOpen] = useState(false);

  // Pro mode modal management with URL parameter handling
  const { isModalOpen: proModal, setModalOpen: setProModal, shouldShowThankYou, isAlreadyBooked, openModalForBookedUser, isBookedUserFlow } = useProModeModal();

  // Refs
  const prevTabRef = useRef<string | null>(null);

  const { session, user } = useAuth();
  const { checkGitHubConnection } = useGitHub();
  const [searchParams, setSearchParams] = useSearchParams();
  const {tier, loading: creditsLoading} = useCredits();

  const isUploadAssetEnabled = useIsUploadAssetEnabled();

  const [embeddedTaskTabId, setEmbeddedTaskTabId] = useState(() => {
    const storedTabId = localStorage.getItem("embeddedTaskTabId");

    if (!storedTabId) {
      const newTabId = generateUniqueTabId();
      localStorage.setItem("embeddedTaskTabId", newTabId);
      return newTabId;
    }
    return storedTabId || generateUniqueTabId();
  });

  const clearEmbeddedTaskTabId = () => {
    const newTabId = generateUniqueTabId();
    localStorage.setItem("embeddedTaskTabId", newTabId);
    setEmbeddedTaskTabId(newTabId);
  };

  const handleFeatureModalClose = (open: boolean) => {
    if (!open) {
      markAssetModalAsSeen();
    }
    setIsFeatureModalOpen(open);
  };

  // Check if user is logged in and hasn't seen the feature modal
  useEffect(() => {
    if (session && user && !hasSeenAssetModal() && isUploadAssetEnabled) {
      setIsFeatureModalOpen(true);
    }
  }, [session, user]);



  const navigate = useNavigate();

  const { setTabs, setActiveTab, updateTabState, state, getTabByJobId, activeTab } =
    useTabState();

  const { refreshCredits } = useCredits();



  useEffect(() => {
    if (session) {
      setLoading(false);

      if (state.activeTab === "home") {
        if (prevTabRef.current !== null && prevTabRef.current !== "home") {
          refreshCredits();
        }
        updateTabState("home", { isCloudFlow: true });
      }
      prevTabRef.current = state.activeTab;
    } else {
      setLoading(false);
    }
  }, [session, state.activeTab, location?.pathname]);

  useEffect(()=>{
    if(activeTab === "home"){
      refetchJobs();
    }
  },[activeTab])

  // Add document click handler to toggle banner visibility
  useEffect(() => {
    const handleDocumentClick = () => {
      if (!isBannerVisible && globalConfig?.overload_lock?.enabled) {
        setIsBannerVisible(true);
      }
    };

    document.addEventListener("click", handleDocumentClick);

    return () => {
      document.removeEventListener("click", handleDocumentClick);
    };
  }, [isBannerVisible, globalConfig]);



  useEffect(()=>{
    if(!user){
      navigate('/login');
    }
  },[location.pathname])

  const handleReload = async () => {
    setReloading(true);
    await refetchJobs();
    setReloading(false);
  };

  useEffect(() => {
    if (session) {
      checkGitHubConnection();
      setLoading(false);

      // Clean up tab states in localStorage when the app starts
      cleanupTabStatesInLocalStorage();
    } else {
      setLoading(false);
    }
  }, [session]);

  const handleJobClick = (job: Job) => {
    if (!job.payload.task) {
      console.error("No task data available");
      return;
    }

    const containerId = job.payload.container_id || job.id;

    if (!containerId) {
      console.error("No container ID available");
      return;
    }

    const existingTab = getTabByJobId(job.id);
    if (existingTab) {
      setActiveTab(existingTab.id);
      return;
    }

    // Generate a unique tab ID for this chat
    const newTabId = `tab-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Construct the full trajectory path
    const trajPath = `/root/runs/${job.id}/trajectory.json`;

    const tabState = {
      containerId: job.payload.container_id || job.id,
      initial_commit_id: job.payload.initial_commit_id,
      task: job.payload.task,
      jobId: job.id,
      title: job.payload?.original_task || job.payload.task,
      trajPath,
      tabId: newTabId,
      fromJobList: true,
      isCloudFlow: job.payload?.is_cloud,
      clientRefId: job.client_ref_id,
      modelName: job.payload.model_name,
      promptName: job.payload.prompt_name,
      promptVersion: job.payload.prompt_version,
      costLimit: job.payload.per_instance_cost_limit,
      agentName: job.payload.agent_name,
      portMapping: job.payload.portMapping,
      created_by: job.created_by,
    };

    // Create new tab
    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title: `${job.payload.task}`,
        path: "/chat",
        state: tabState,
      },
    ]);

    // Update tab state
    updateTabState(newTabId, tabState);

    // Set active tab and navigate
    setActiveTab(newTabId);
    navigate(`/chat?id=${job.id}`);
  };

  const getTimeUntil = () => {
    const now = new Date();
    const lockedUntil = new Date(
      globalConfig?.overload_lock?.locked_until || ""
    );
    const diff = lockedUntil.getTime() - now.getTime();

    // Calculate days, hours, and minutes without overlap
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    // Pad with leading zeros if single digit
    const paddedDays = days < 10 ? `0${days}` : `${days}`;
    const paddedHours = hours < 10 ? `0${hours}` : `${hours}`;
    const paddedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`;

    return {
      days: paddedDays,
      hours: paddedHours,
      minutes: paddedMinutes,
    };
  };

  // Handle job_id query parameter
  useEffect(() => {
    const jobId = searchParams.get("job_id");

    if (jobId && jobs.length > 0) {
      const jobToOpen = jobs.find((job) => job.id === jobId);

      if (jobToOpen) {
        // Call handleJobClick with the found job
        handleJobClick(jobToOpen);

        // Remove the job_id parameter from URL
        searchParams.delete("job_id");
        setSearchParams(searchParams);
      }
    }
  }, [jobs, searchParams, setSearchParams, handleJobClick]);

  let userName;

  if (user?.user_metadata?.custom_name) {
    userName = user.user_metadata.custom_name;
  } else if (user?.user_metadata?.full_name) {
    userName = user.user_metadata.full_name.split(" ")[0];
  } else {
    userName = user?.email?.split("@")[0];
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4 text-center">
          <Spinner className="mx-auto" />
          <p className="text-sm text-muted-foreground">Loading</p>
        </div>
      </div>
    );
  }

  // Animation variants for text
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
      },
    },
  };

  // Gradient text animation
  const gradientVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <>
      <div className="relative flex flex-col items-center justify-center h-full overflow-y-scroll md:h-screen md:block">
        {!creditsLoading && tier !== "pro_mode" && (
          <ProBanner modalChange={setProModal} isAlreadyBooked={isAlreadyBooked} onBookedUserClick={openModalForBookedUser} />
        )}
        <AnimatePresence>
          {isBannerVisible && (
            <motion.div
              className="absolute top-0 left-0 z-20 w-full"
              initial={{ y: "-100%" }}
              animate={{ y: 0 }}
              exit={{ y: "-100%" }}
              transition={{ type: "spring", stiffness: 100, damping: 20 }}
            >
              <div className="relative min-h-[300px] sm:min-h-[450px] lg:min-h-[400px]">
                <img
                  alt="Banner"
                  src={Banner}
                  className="absolute inset-0 z-20 object-cover w-full h-full"
                />
                <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
                  <div className="flex w-full h-full max-md:hidden">
                    {[...Array(4)].map((_, index) => (
                      <video
                        key={index}
                        src={BgVideo}
                        autoPlay
                        loop
                        muted
                        className="flex-shrink-0 object-cover w-full h-full opacity-10"
                        style={{
                          maxWidth: "25%",
                          transform: `rotate(${index * 180}deg)`,
                        }}
                      />
                    ))}
                  </div>
                </div>
                <div className="absolute top-0 left-0 z-30 w-full h-full pb-10">
                  <div className="flex px-4 py-6 w-full min-h-[300px] sm:min-h-[750px] lg:min-h-[400px] sm:px-8 sm:py-8 lg:px-[72px] lg:py-12">
                    <div className="flex flex-col items-start justify-between w-full space-y-8">
                      {/* Title and Description */}
                      <div className="flex flex-col space-y-2 max-w-full lg:max-w-[600px]">
                        <span className="text-[#242F33] text-lg font-semibold leading-6 font-sans sm:text-xl sm:leading-7 lg:text-[28px] lg:leading-9">
                          {globalConfig?.overload_lock?.title}
                        </span>
                        <span className="text-[#242F33] text-sm font-normal leading-5 opacity-60 font-sans sm:text-base sm:leading-6 lg:text-[18px] lg:leading-7">
                          {globalConfig?.overload_lock?.description}
                        </span>
                      </div>

                      {/* Bottom Content */}
                      <div className="flex flex-col w-full gap-6 lg:flex-row lg:items-center lg:justify-between lg:gap-0">
                        {/* Stats Section */}
                        <div className="flex flex-col gap-4 sm:flex-row sm:gap-8 lg:gap-16">
                          <div className="flex flex-col items-start gap-1 lg:gap-2">
                            <div className="flex items-center gap-2">
                              <span className="text-[#242F33] text-xl font-semibold leading-6 font-sans sm:text-2xl sm:leading-7 lg:text-[28px] lg:leading-9">
                                {globalConfig?.overload_lock?.capacity}
                              </span>
                              <PulseDot color="#CB6565" />
                            </div>
                            <span className="text-[#242F33] text-sm font-normal leading-5 opacity-60 font-sans sm:text-base sm:leading-6 lg:text-[18px] lg:leading-7">
                              System Capacity
                            </span>
                          </div>
                          <div className="flex flex-col items-start gap-1 lg:gap-2">
                            <div className="flex items-center gap-2">
                              <span className="text-[#242F33] text-xl font-semibold leading-6 font-sans sm:text-2xl sm:leading-7 lg:text-[28px] lg:leading-9">
                                {globalConfig?.overload_lock?.concurrent_tasks}
                              </span>
                            </div>
                            <span className="text-[#242F33] text-sm font-normal leading-5 opacity-60 font-sans sm:text-base sm:leading-6 lg:text-[18px] lg:leading-7">
                              Concurring running tasks
                            </span>
                          </div>
                        </div>

                        {/* Countdown Section */}
                        <div className="flex flex-col gap-1 lg:gap-1">
                          <div className="flex items-center gap-2 lg:justify-end">
                            <div className="p-2 bg-white rounded-lg flex items-center justify-center lg:p-[10px] lg:rounded-[12px]">
                              <span className="text-[#3D494D] text-lg font-semibold leading-6 font-sans sm:text-xl sm:leading-7 lg:text-[28px] lg:leading-9">
                                {getTimeUntil().days}
                                <span className="text-xs lg:text-sm">d</span>
                              </span>
                            </div>
                            <div className="p-2 bg-white rounded-lg flex items-center justify-center lg:p-[10px] lg:rounded-[12px]">
                              <span className="text-[#3D494D] text-lg font-semibold leading-6 font-sans sm:text-xl sm:leading-7 lg:text-[28px] lg:leading-9">
                                {getTimeUntil().hours}
                                <span className="text-xs lg:text-sm">h</span>
                              </span>
                            </div>
                            <div className="p-2 bg-white rounded-lg flex items-center justify-center lg:p-[10px] lg:rounded-[12px]">
                              <span className="text-[#3D494D] text-lg font-semibold leading-6 font-sans sm:text-xl sm:leading-7 lg:text-[28px] lg:leading-9">
                                {getTimeUntil().minutes}
                                <span className="text-xs lg:text-sm">m</span>
                              </span>
                            </div>
                          </div>
                          <span className="text-[#242F33] text-sm font-normal leading-5 opacity-60 font-sans sm:text-base sm:leading-6 lg:text-[18px] lg:leading-7 lg:text-right">
                            Estimated time to come back
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="h-12 bg-black/20 backdrop-blur-sm sm:h-16 lg:h-20"></div>
            </motion.div>
          )}
        </AnimatePresence>
        <div className="mx-auto md:p-6 lg:max-w-6xl">
          <div className="mt-0 md:mt-20">
            <div className="max-w-4xl pb-0 mx-auto">
              <Card className="p-6 pb-0 bg-transparent border-0 md:space-y-1 pt-[5vh] md:pt-[0] md:mb-[0]">
                <CardHeader className="p-0">
                  <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="w-full"
                  >
                    <CardDescription className="flex items-center justify-center w-full h-6 md:py-1 font-brockmann">
                      <motion.span
                        variants={itemVariants as any}
                        className="w-full text-[28px] md:text-[40px] font-medium text-white/50 capitalize"
                      >
                        Welcome, {session ? userName : ""}
                      </motion.span>
                    </CardDescription>
                    <CardTitle className="flex items-center justify-between w-full mt-2 font-medium md:mt-0 font-brockmann">
                      <motion.span
                        variants={gradientVariants as any}
                        className="animate-text bg-gradient-to-r text-[28px] md:text-[40px] md:mt-[8px] from-[#80FFF9] to-[#F7E7D9] text-transparent bg-clip-text"
                      >
                        What will you build today?
                      </motion.span>
                    </CardTitle>
                  </motion.div>
                </CardHeader>
              </Card>

              <EmbeddedTask
                tabId={embeddedTaskTabId}
                onClearTabId={clearEmbeddedTaskTabId}
                isReferralModalOpen={isReferralModalOpen}
                setIsReferralModalOpen={setIsReferralModalOpen}
                isCustomAgentsModalOpen={isCustomAgentsModalOpen}
                setIsCustomAgentsModalOpen={setIsCustomAgentsModalOpen}
              />
            </div>

            {user && session && (
              <motion.div
                className=" hidden md:block px-4 my-8 mb-[4rem]"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  type: "spring",
                  stiffness: 100,
                  damping: 10,
                  delay: 0.6, // Delay after the welcome animations
                }}
              >
                <RecentTasks
                  jobs={jobs}
                  handleJobClick={handleJobClick}
                  handleReload={handleReload}
                  reloading={reloading}
                  setIsJobIdDialogOpen={setIsJobIdDialogOpen}
                  loading={jobLoading}
                />
              </motion.div>
            )}
          </div>

          <FadingDotLine />

          <ShowCase showCaseData={globalConfig?.portfolio as any || []} />

          <JobIDModal
            isJobIdDialogOpen={isJobIdDialogOpen}
            setIsJobIdDialogOpen={setIsJobIdDialogOpen}
            jobIdInput={jobIdInput}
            setJobIdInput={setJobIdInput}
            handleJobClick={handleJobClick}
          />

        </div>


        <ReferralModal
          isOpen={isReferralModalOpen}
          onOpenChange={setIsReferralModalOpen}
        />

        <WelcomeScreen />

        <ProModal isOpen={proModal} onOpenChange={setProModal} shouldShowThankYou={shouldShowThankYou} isBookedUserFlow={isBookedUserFlow} />
      </div>
    </>
  );
}
